# Chapter-Based Image Card Placement Testing Guide

## 🎯 Implementation Summary

The image suggestion card placement system has been upgraded from **paragraph-based intervals** to **chapter-based targeting**. This provides more precise and contextually appropriate placement of image suggestions.

### **Key Changes Made:**

1. **New Placement Strategy**: Image cards now appear immediately before chapter headings
2. **Chapter Detection**: Uses regex to find `<h1>` or `<h2>` tags containing "Chapter X:" patterns
3. **Skip Chapter 1**: Maintains the existing rule to skip Chapter 1
4. **Fallback System**: Falls back to legacy paragraph-based placement if no chapter headings are found

## 🧪 Testing Checklist

### **Automated Testing**
```javascript
// Run in browser console after loading the app
import { runChapterPlacementTests } from './src/utils/testChapterPlacement.js';
runChapterPlacementTests();
```

### **Manual Browser Testing**

#### **Step 1: Create Test Document**
1. Navigate to http://localhost:4028
2. Create a new document (any type)
3. Complete the wizard through Step 7 (Document Outline)
4. Generate an outline with at least 3-4 chapters
5. Approve the outline
6. **Enable image suggestions** (toggle ON)
7. Complete Step 8 and generate the document

#### **Step 2: Validate Placement in Document Editor**
1. Open the generated document in the editor
2. Scroll through the content and verify:

**✅ Expected Behavior:**
- **NO** image card before Chapter 1 heading
- **YES** image card before Chapter 2 heading  
- **YES** image card before Chapter 3 heading
- **YES** image card before Chapter 4+ headings

**❌ Issues to Watch For:**
- Image cards appearing before Chapter 1
- Image cards missing before other chapters
- Cards appearing in wrong locations (middle of paragraphs)
- Multiple cards for the same chapter

#### **Step 3: Test Image Card Functionality**
1. Click on image suggestion cards
2. Verify image selection works
3. Test image insertion into document
4. Confirm cards are removed after image selection

### **Console Debugging**

Look for these debug messages in the browser console:

```
🔍 DEBUG: Available chapters for cards: ["chapter-2", "chapter-3", "chapter-4"]
🔍 DEBUG: Found X chapter headings: [chapter details]
🔍 DEBUG: Target headings for image cards: [filtered list]
✅ Successfully injected image card for chapter-X before "Chapter Title"
```

### **Edge Case Testing**

#### **Test Case 1: Document with Only 1 Chapter**
- Create a document with only Chapter 1
- Verify NO image cards appear

#### **Test Case 2: Document with No Chapter Headings**
- Test with content that doesn't follow chapter format
- Should fall back to legacy paragraph-based placement

#### **Test Case 3: Mixed Heading Formats**
- Test with different heading tags (H1, H2)
- Test with variations like "Chapter 2 Title" (no colon)

## 🔧 Implementation Details

### **New Chapter Detection Logic**
```javascript
// Enhanced regex pattern
const chapterHeadingRegex = /<(h[1-6])[^>]*>(\s*Chapter\s+(\d+)(?::\s*[^<]*)?)<\/h[1-6]>/gi;
```

**Matches:**
- `<h2>Chapter 2: Understanding Basics</h2>`
- `<h1>Chapter 3: Advanced Topics</h1>`
- `<h2>  Chapter   4  :   Final Chapter  </h2>`
- `<h2>Chapter 2 Understanding Basics</h2>` (no colon)

### **Placement Algorithm**
1. Parse HTML to find all chapter headings
2. Filter to chapters 2+ that have image suggestions
3. Process in reverse order (to maintain positions)
4. Insert image card HTML immediately before each heading
5. Fall back to legacy method if no headings found

### **Data Structure Compatibility**
The new system maintains full compatibility with existing:
- Image suggestion data structure
- UI components (ImageSuggestionCard, etc.)
- Image selection and insertion functionality

## 🚨 Troubleshooting

### **Problem: No Image Cards Appearing**
**Check:**
- Image suggestions are enabled in Step 7
- Console shows available chapters
- Chapter headings follow expected format
- Image suggestion data exists

### **Problem: Cards in Wrong Locations**
**Check:**
- Console debug messages for placement positions
- HTML structure of generated content
- Regex matching of chapter headings

### **Problem: Cards Before Chapter 1**
**Check:**
- Filter logic in `availableChapters`
- Chapter number extraction from headings
- Skip logic for chapter-1

## 📊 Success Criteria

**✅ Implementation is successful if:**
1. Image cards appear before Chapter 2, 3, 4+ headings
2. NO image cards appear before Chapter 1
3. Cards are positioned immediately before chapter headings
4. All existing image functionality works (selection, insertion)
5. Fallback works when chapter headings aren't found
6. Console shows clear debug information

## 🎉 Benefits of New System

1. **More Precise Placement**: Cards appear at natural chapter boundaries
2. **Better User Experience**: Images suggested at logical transition points
3. **Contextual Relevance**: Each card is tied to specific chapter content
4. **Maintainable Code**: Clear separation between placement logic and UI
5. **Robust Fallback**: Legacy system ensures cards always appear somewhere

## 🔄 Rollback Plan

If issues arise, the legacy paragraph-based system is preserved as `injectChapterImageCardsLegacy()` and can be quickly restored by modifying the main function to always use the fallback method.
