import React, { useEffect, useState, useCallback, useRef } from 'react';
import { EditorContent, useEditor, ReactNodeViewRenderer } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Image from '@tiptap/extension-image';
import { convertAIContentToHTML } from '../../../utils/contentConverter';
import ImageUrlInput from './ImageUrlInput';
import ContextualImageSelectionModal from './ContextualImageSelectionModal';
import { ImageSuggestionCardExtension } from './ImageSuggestionCardExtension';
import useScrollAwarePosition from '../../../hooks/useScrollAwarePosition';

/**
 * DocumentCanvasMinimal - A minimal, clean Tiptap editor component
 *
 * This is a simplified version of DocumentCanvas that provides:
 * - Basic rich text editing with Tiptap
 * - Enhanced placeholder system with "regular text" placeholder
 * - AI content loading and conversion from markdown to HTML
 * - Content change handling and persistence
 * - Clean, centered layout with responsive design
 * - Minimal dependencies and complexity
 * - Foundation for incremental feature additions
 */
const DocumentCanvasMinimal = ({
  content = null,
  onContentChange = null,
  isLoading = false,
  isReadOnly = false,
  imageSuggestions = {},
  onOpenImageModal = null
}) => {
  // State for two-stage floating menu
  const [isMenuExpanded, setIsMenuExpanded] = useState(false);
  const [showFloatingMenu, setShowFloatingMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [expandedMenuPosition, setExpandedMenuPosition] = useState({ top: 0, left: 0 });
  const [preventAutoClose, setPreventAutoClose] = useState(false);

  // State for image URL input
  const [showImageUrlInput, setShowImageUrlInput] = useState(false);
  const [imageUrlInputPosition, setImageUrlInputPosition] = useState({ top: 0, left: 0 });

  // State for contextual image selection modal
  const [showContextualImageModal, setShowContextualImageModal] = useState(false);
  const [contextualImageContext, setContextualImageContext] = useState(null);

  // Refs for scroll-aware positioning
  const currentTargetElementRef = useRef(null);
  const currentNodePositionRef = useRef(null);
  
  // Refs for managing menu visibility during scroll
  const menuVisibilityTimeoutRef = useRef(null);
  const lastVisibilityStateRef = useRef(true);
  
  // Refs for expanded menu and trigger button click-outside detection
  const expandedMenuRef = useRef(null);
  const triggerButtonRef = useRef(null);
  
  // Ref to track if content has been set to prevent infinite loops
  const contentSetRef = useRef(false);
  const lastContentRef = useRef(null);

  // Centralized menu closing function to ensure consistent behavior
  // Note: editor dependency will be added after editor initialization
  const closeExpandedMenu = useCallback((reason = 'unknown', preserveFocus = true) => {
    // Verify menu is actually expanded before closing
    if (!isMenuExpanded) {
      return;
    }
    
    // Update menu state using existing state management
    setIsMenuExpanded(false);
    
    // Reset preventAutoClose flag if it was set to ensure clean state
    if (preventAutoClose) {
      setPreventAutoClose(false);
    }
  }, [preventAutoClose, isMenuExpanded]);

  // Function to recalculate menu positions during scroll events
  const recalculateMenuPosition = useCallback((scrollInfo) => {
    if (!currentTargetElementRef.current || !showFloatingMenu) return;

    const element = currentTargetElementRef.current;
    const rect = element.getBoundingClientRect();

    // Handle visibility changes more gracefully with debouncing
    if (scrollInfo.visibilityChanged) {
      // Clear any pending visibility timeout
      if (menuVisibilityTimeoutRef.current) {
        clearTimeout(menuVisibilityTimeoutRef.current);
        menuVisibilityTimeoutRef.current = null;
      }

      if (!scrollInfo.isVisible) {
        // Element went out of view - debounce hiding to prevent flicker during scroll
        console.log('🔄 Element went out of view, scheduling menu hide');
        lastVisibilityStateRef.current = false;
        
        menuVisibilityTimeoutRef.current = setTimeout(() => {
          // Only hide if still not visible after delay
          if (!lastVisibilityStateRef.current) {
            console.log('🔄 Hiding menu after visibility timeout');
            setShowFloatingMenu(false);
          }
        }, 150); // 150ms delay to prevent flicker during scroll
        return;
      } else {
        // Element came back into view - restore menu immediately
        console.log('🔄 Element came back into view, restoring menu');
        lastVisibilityStateRef.current = true;
        setShowFloatingMenu(true);
        // Continue with position recalculation below
      }
    }

    // Check for invalid dimensions during normal scroll (not visibility changes)
    if (!scrollInfo.visibilityChanged && (rect.width === 0 || rect.height === 0)) {
      console.log('🔄 Invalid element dimensions during scroll, skipping update');
      return; // Don't hide menu, just skip this update
    }

    // Additional safety check - ensure element is still in the DOM
    if (!document.contains(element)) {
      console.log('🔄 Target element no longer in DOM, hiding menu');
      setShowFloatingMenu(false);
      currentTargetElementRef.current = null;
      return;
    }

    console.log('🔄 SCROLL RECALCULATION:', {
      scrollY: scrollInfo.scrollY,
      elementRect: rect,
      elementTag: element.tagName
    });

    // Recalculate button position using the same logic as handleSelectionUpdate
    const viewportWidth = window.innerWidth;

    // Calculate positioning similar to Designrr's approach
    let leftOffset;

    if (viewportWidth <= 768) {
      // Mobile: Smaller offset to prevent going off-screen
      leftOffset = 16; // -left-4 equivalent (16px)
    } else if (viewportWidth <= 1024) {
      // Tablet: Medium offset
      leftOffset = 32; // -left-8 equivalent (32px)
    } else {
      // Desktop: Full offset like Designrr (-50px)
      leftOffset = 50;
    }

    // Position to the left of the content block (absolute positioning relative to document)
    const editorContainer = document.querySelector('.tiptap-editor');
    const containerRect = editorContainer ? editorContainer.getBoundingClientRect() : { top: 0, left: 0 };

    const newButtonPosition = {
      top: rect.top - containerRect.top,
      left: rect.left - containerRect.left - leftOffset
    };

    // Calculate new expanded menu position
    const newExpandedPosition = calculateExpandedMenuPosition(rect, newButtonPosition);

    // Update positions
    setMenuPosition(newButtonPosition);
    setExpandedMenuPosition(newExpandedPosition);

    console.log('🔄 Menu position recalculated during scroll:', {
      scrollY: scrollInfo.scrollY,
      newButtonPosition,
      newExpandedPosition
    });
  }, [showFloatingMenu]);

  // Helper function to calculate optimal expanded menu position
  const calculateExpandedMenuPosition = (buttonRect, buttonPosition) => {
    const menuWidth = 200; // min-w-[200px]
    const menuHeight = 300; // Estimated height for menu with options
    const spacing = 8; // Gap between button and menu
    const buttonHeight = 32; // button height (32px)
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const scrollY = window.scrollY;

    // Enhanced margins for better mobile experience
    const marginX = Math.max(20, viewportWidth < 768 ? 16 : 20); // Larger margins on mobile
    const marginY = Math.max(20, viewportWidth < 768 ? 16 : 20);

    // Try positioning below the button first (preferred dropdown style)
    const belowPosition = {
      top: buttonPosition.top + buttonHeight + spacing,
      left: buttonPosition.left // Align with button left edge
    };

    // Check if menu fits below and within viewport horizontally
    const fitsBelow = (belowPosition.top + menuHeight) <= (viewportHeight + scrollY - marginY);
    const fitsHorizontally = (belowPosition.left + menuWidth) <= (viewportWidth - marginX);

    if (fitsBelow && fitsHorizontally) {
      console.log('🎯 Menu positioned below button (preferred)');
      return belowPosition;
    }

    // If menu extends beyond right edge, adjust left position while keeping it below
    if (fitsBelow && !fitsHorizontally) {
      const adjustedBelowPosition = {
        top: belowPosition.top,
        left: Math.max(marginX, viewportWidth - menuWidth - marginX) // Position from right edge
      };
      console.log('🎯 Menu positioned below button with horizontal adjustment');
      return adjustedBelowPosition;
    }

    // Try positioning above the button if below doesn't fit
    const abovePosition = {
      top: buttonPosition.top - menuHeight - spacing,
      left: buttonPosition.left
    };

    // Check if menu fits above
    const fitsAbove = (abovePosition.top) >= (scrollY + marginY);
    const fitsAboveHorizontally = (abovePosition.left + menuWidth) <= (viewportWidth - marginX);

    if (fitsAbove && fitsAboveHorizontally) {
      console.log('🎯 Menu positioned above button');
      return abovePosition;
    }

    // If above fits vertically but not horizontally, adjust left position
    if (fitsAbove && !fitsAboveHorizontally) {
      const adjustedAbovePosition = {
        top: abovePosition.top,
        left: Math.max(marginX, viewportWidth - menuWidth - marginX)
      };
      console.log('🎯 Menu positioned above button with horizontal adjustment');
      return adjustedAbovePosition;
    }

    // Try positioning to the right of the button (fallback for very constrained spaces)
    const rightPosition = {
      top: buttonPosition.top,
      left: buttonPosition.left + buttonHeight + spacing
    };

    // Check if menu fits to the right
    const fitsRight = (rightPosition.left + menuWidth) <= (viewportWidth - marginX);

    if (fitsRight) {
      console.log('🎯 Menu positioned to the right of button (fallback)');
      return rightPosition;
    }

    // Try positioning to the left of the button
    const leftPosition = {
      top: buttonPosition.top,
      left: buttonPosition.left - menuWidth - spacing
    };

    // Check if menu fits to the left
    const fitsLeft = leftPosition.left >= marginX;

    if (fitsLeft) {
      console.log('🎯 Menu positioned to the left of button (fallback)');
      return leftPosition;
    }

    // Final fallback: position below but adjust to fit in viewport (emergency positioning)
    const fallbackPosition = {
      top: Math.max(marginY + scrollY, Math.min(belowPosition.top, viewportHeight + scrollY - menuHeight - marginY)),
      left: Math.max(marginX, Math.min(buttonPosition.left, viewportWidth - menuWidth - marginX))
    };

    console.log('🎯 Menu positioned using emergency fallback positioning');
    return fallbackPosition;
  };

  // Initialize scroll-aware positioning hook
  const { updatePosition: updateScrollPosition } = useScrollAwarePosition({
    onPositionUpdate: recalculateMenuPosition,
    targetElement: currentTargetElementRef.current,
    enabled: showFloatingMenu && !isReadOnly,
    throttleMs: 16 // 60fps for smooth updates
  });

  // Click-outside detection for expanded menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      try {
        // Validate event and event.target exist
        if (!event || !event.target) {
          console.warn('🔍 Invalid event or event.target in click-outside detection');
          return;
        }

        // Ensure event.target is a valid DOM node
        if (!(event.target instanceof Node)) {
          console.warn('🔍 Event target is not a valid DOM node');
          return;
        }

        // Add null reference protection for expandedMenuRef.current
        if (!expandedMenuRef.current) {
          console.warn('🔍 Expanded menu ref is null, skipping click-outside detection');
          return;
        }

        // Check if menu is currently expanded before proceeding
        if (!isMenuExpanded) {
          return;
        }

        // Check if preventAutoClose flag is set (respects menu locking)
        if (preventAutoClose) {
          return;
        }

        // Check if click target is outside both the expanded menu AND the trigger button
        const isClickInsideMenu = expandedMenuRef.current.contains(event.target);
        const isClickInsideTrigger = triggerButtonRef.current && triggerButtonRef.current.contains(event.target);
        const isClickOutside = !isClickInsideMenu && !isClickInsideTrigger;

        if (isClickOutside) {
          // Use basic menu closing function (focus will be handled separately if needed)
          closeExpandedMenu('click-outside', false);
        }
      } catch (error) {
        console.error('Error in click-outside detection:', error);
        // Gracefully handle errors by not closing the menu to avoid unexpected behavior
      }
    };

    // Only add event listeners when expanded menu is open
    if (isMenuExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside); // Mobile support
      
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('touchstart', handleClickOutside);
      };
    }
  }, [isMenuExpanded, preventAutoClose, closeExpandedMenu]);

  // Helper function to determine menu type based on current editor state
  const getCurrentMenuType = () => {
    if (!editor) {
      return 'plus';
    }

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();

    // Define supported node types for floating menu
    const supportedNodeTypes = ['paragraph', 'heading', 'listItem', 'blockquote', 'codeBlock', 'image'];

    if (supportedNodeTypes.includes(currentNode.type.name)) {
      const isEmpty = currentNode.content.size === 0;
      const menuType = isEmpty ? 'plus' : 'ellipsis';
      return menuType;
    }

    return 'plus';
  };

  // Helper function to get current node type for menu customization
  const getCurrentNodeType = () => {
    if (!editor) return 'paragraph';

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();

    return currentNode.type.name;
  };

  // Image handlers - prioritize AI suggestions over URL input
  const handleShowImageOptions = () => {
    // Prevent image interactions in read-only mode
    if (isReadOnly) {
      console.log('🔒 Read-only mode: Image options disabled');
      return;
    }

    // Check if we have AI-generated image suggestions
    const hasImageSuggestions = imageSuggestions && Object.keys(imageSuggestions).length > 0;

    if (hasImageSuggestions && onOpenImageModal) {
      // Use AI suggestions - open modal with first available chapter
      const firstChapterId = Object.keys(imageSuggestions)[0];
      onOpenImageModal(firstChapterId);
      closeExpandedMenu('image-modal-open', true);
    } else {
      // Fallback to URL input
      setImageUrlInputPosition({
        top: expandedMenuPosition.top,
        left: expandedMenuPosition.left + 220 // Position to the right of expanded menu
      });
      setShowImageUrlInput(true);
      closeExpandedMenu('image-url-input', true);
    }
  };

  const handleImageInsert = (imageData) => {
    editor.chain().focus().setImage({
      src: imageData.src,
      alt: imageData.alt
    }).run();
    setShowImageUrlInput(false);
  };

  const handleImageUrlCancel = () => {
    setShowImageUrlInput(false);
  };

  // Image-specific menu handlers
  const handleEditImageAltText = () => {
    if (!editor) return;

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();

    if (currentNode.type.name === 'image') {
      const currentAlt = currentNode.attrs.alt || '';
      const newAlt = prompt('Enter alt text for the image:', currentAlt);

      if (newAlt !== null) { // User didn't cancel
        editor.chain().focus().updateAttributes('image', { alt: newAlt }).run();
        console.log('🖼️ Updated image alt text:', newAlt);
      }
    }
  };

  const handleReplaceImage = () => {
    if (!editor) return;

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();

    if (currentNode.type.name === 'image') {
      const currentSrc = currentNode.attrs.src || '';
      const currentAlt = currentNode.attrs.alt || '';
      const newSrc = prompt('Enter new image URL:', currentSrc);

      if (newSrc !== null && newSrc.trim()) { // User didn't cancel and provided URL
        editor.chain().focus().updateAttributes('image', {
          src: newSrc.trim(),
          alt: currentAlt // Keep existing alt text
        }).run();
        console.log('🖼️ Replaced image with:', newSrc);
      }
    }
  };

  const handleResizeImage = (size) => {
    if (!editor) return;

    const sizeClasses = {
      small: 'tiptap-image max-w-xs h-auto rounded-lg shadow-sm', // ~320px max
      medium: 'tiptap-image max-w-md h-auto rounded-lg shadow-sm', // ~448px max
      large: 'tiptap-image max-w-2xl h-auto rounded-lg shadow-sm', // ~672px max
      full: 'tiptap-image max-w-full h-auto rounded-lg shadow-sm' // Full width
    };

    const className = sizeClasses[size] || sizeClasses.medium;

    editor.chain().focus().updateAttributes('image', {
      class: className
    }).run();

    console.log(`🖼️ Resized image to ${size}`);
  };

  // Contextual image suggestion card handlers
  const handleContextualImageCardClick = (chapterId, placementId, placement) => {
    console.log('🎯 Contextual image card clicked:', { chapterId, placementId, placement });

    setContextualImageContext({
      chapterId,
      placementId,
      placement
    });
    setShowContextualImageModal(true);
  };

  const handleContextualImageSelect = async (selectedImage, placement, chapterId, placementId) => {
    console.log('🖼️ Contextual image selected:', { selectedImage, placement, chapterId, placementId });

    if (!editor || !selectedImage) return;

    try {
      // Find the card element to determine insertion position
      const cardElement = document.querySelector(`[data-chapter-id="${chapterId}"][data-placement-id="${placementId}"]`);

      if (cardElement) {
        // Insert image at the card's location
        // For now, we'll insert at current cursor position and remove the card
        editor.chain().focus().setImage({
          src: selectedImage.url,
          alt: selectedImage.description || `Image for ${chapterId}`,
          class: 'tiptap-image max-w-full h-auto rounded-lg shadow-sm'
        }).run();

        // Remove the card from the DOM
        cardElement.remove();

        console.log('🖼️ Image inserted and card removed successfully');
      }
    } catch (error) {
      console.error('Error inserting contextual image:', error);
    }
  };

  const handleContextualImageModalClose = () => {
    setShowContextualImageModal(false);
    setContextualImageContext(null);
  };



  // Helper function to render node-specific menu options
  const renderNodeSpecificMenuOptions = (nodeType, isEmptyNode) => {
    if (isEmptyNode) {
      // Plus menu options - same for all node types
      return (
        <>
          <button
            onClick={() => {
              editor.chain().focus().toggleHeading({ level: 2 }).run();
              closeExpandedMenu('menu-action', false); // Don't preserve focus since editor.chain().focus() already called
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="font-bold mr-3">H2</span>
            <span className="text-gray-600">Heading</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleHeading({ level: 3 }).run();
              closeExpandedMenu('menu-action', false); // Don't preserve focus since editor.chain().focus() already called
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="font-bold mr-3">H3</span>
            <span className="text-gray-600">Heading</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().setParagraph().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="mr-3">T</span>
            <span className="text-gray-600">Regular text</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleBulletList().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="mr-3">•</span>
            <span className="text-gray-600">Bulleted list</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleOrderedList().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="mr-3">1.</span>
            <span className="text-gray-600">Ordered list</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleBlockquote().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="mr-3">"</span>
            <span className="text-gray-600">Quote</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleCodeBlock().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="mr-3 font-mono">&lt;/&gt;</span>
            <span className="text-gray-600">Code</span>
          </button>

          <button
            onClick={handleShowImageOptions}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
          >
            <span className="mr-3">🖼️</span>
            <span className="text-gray-600">
              {imageSuggestions && Object.keys(imageSuggestions).length > 0 ? 'AI Images' : 'Image'}
            </span>
          </button>
        </>
      );
    }

    // Ellipsis menu options - different for each node type
    switch (nodeType) {
      case 'listItem':
        return (
          <>
            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">T</span>
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleBulletList().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">•</span>
              <span className="text-gray-600">Convert to bullet list</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleOrderedList().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">1.</span>
              <span className="text-gray-600">Convert to ordered list</span>
            </button>

            <button
              onClick={() => {
                // Indent list item (if possible)
                editor.chain().focus().sinkListItem('listItem').run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">→</span>
              <span className="text-gray-600">Indent</span>
            </button>

            <button
              onClick={() => {
                // Outdent list item (if possible)
                editor.chain().focus().liftListItem('listItem').run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">←</span>
              <span className="text-gray-600">Outdent</span>
            </button>
          </>
        );

      case 'blockquote':
        return (
          <>
            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">T</span>
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 2 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="font-bold mr-3">H2</span>
              <span className="text-gray-600">Convert to Heading 2</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 3 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="font-bold mr-3">H3</span>
              <span className="text-gray-600">Convert to Heading 3</span>
            </button>
          </>
        );

      case 'codeBlock':
        return (
          <>
            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">T</span>
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                // Convert to inline code (simplified approach)
                const content = editor.getHTML();
                const textContent = editor.getText();
                editor.chain().focus().setParagraph().run();
                editor.chain().focus().insertContent(`<code>${textContent}</code>`).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3 font-mono">`</span>
              <span className="text-gray-600">Convert to inline code</span>
            </button>
          </>
        );

      case 'image':
        return (
          <>
            <button
              onClick={() => {
                handleEditImageAltText();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">✏️</span>
              <span className="text-gray-600">Edit alt text</span>
            </button>

            <button
              onClick={() => {
                handleReplaceImage();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">🔄</span>
              <span className="text-gray-600">Replace image</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('small');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">📏</span>
              <span className="text-gray-600">Resize to small</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('medium');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">📐</span>
              <span className="text-gray-600">Resize to medium</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('large');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">📊</span>
              <span className="text-gray-600">Resize to large</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('full');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">↔️</span>
              <span className="text-gray-600">Full width</span>
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={() => {
                editor.chain().focus().deleteSelection().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center text-red-600"
            >
              <span className="mr-3">🗑️</span>
              <span>Delete image</span>
            </button>
          </>
        );

      default:
        // Default ellipsis menu for paragraphs and headings
        return (
          <>
            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 2 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="font-bold mr-3">H2</span>
              <span className="text-gray-600">Convert to Heading 2</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 3 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="font-bold mr-3">H3</span>
              <span className="text-gray-600">Convert to Heading 3</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">T</span>
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleBulletList().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">•</span>
              <span className="text-gray-600">Convert to bullet list</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleBlockquote().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3">"</span>
              <span className="text-gray-600">Convert to quote</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleCodeBlock().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            >
              <span className="mr-3 font-mono">&lt;/&gt;</span>
              <span className="text-gray-600">Convert to code</span>
            </button>
          </>
        );
    }
  };

  // Convert AI content to HTML for initial editor content
  const getInitialContent = () => {
    if (content) {
      const htmlContent = convertAIContentToHTML(content, imageSuggestions, isReadOnly);
      return htmlContent;
    }
    return '<p></p>'; // Empty paragraph to show placeholder
  };

  // Initialize Tiptap editor with enhanced configuration
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: isReadOnly ? '' : 'regular text',
        showOnlyWhenEditable: true,
        showOnlyCurrent: false,
      }),
      Image.configure({
        inline: false,
        allowBase64: true,
        HTMLAttributes: {
          class: 'tiptap-image max-w-full h-auto rounded-lg shadow-sm',
        },
      }),
      // Enable image suggestion cards with React Node Views only in edit mode
      ...(isReadOnly ? [] : [ImageSuggestionCardExtension]),
    ],
    content: getInitialContent(),
    editable: !isReadOnly,
    onCreate: ({ editor }) => {
      // Set up editor storage for image suggestion card communication
      editor.storage.imageModal = {
        onOpen: onOpenImageModal || ((chapterId, placementId, placement) => {
          // Fallback handler if no onOpenImageModal provided
        })
      };
    },
    onUpdate: ({ editor }) => {
      // Call content change handler if provided (only when editable)
      if (onContentChange && !isReadOnly) {
        const html = editor.getHTML();
        console.log('Editor content updated, calling onContentChange');
        onContentChange(html);
      }
    },
    editorProps: {
      attributes: {
        class: `prose prose-base max-w-none w-full focus:outline-none transition-all duration-200 hover:prose-headings:text-blue-700 ${isReadOnly ? 'cursor-default' : ''}`,
        spellcheck: isReadOnly ? 'false' : 'true',
        'data-testid': 'tiptap-editor',
        'data-readonly': isReadOnly ? 'true' : 'false',
      },
    },
  });

  // Process image suggestion placeholders and style them
  const processImageSuggestionPlaceholders = useCallback(() => {
    if (!editor) return;

    // Find all paragraphs that contain our placeholder text
    const editorElement = editor.view.dom;
    const paragraphs = editorElement.querySelectorAll('p');

    paragraphs.forEach(p => {
      const text = p.textContent || '';
      if (text.includes('IMAGE_SUGGESTION_PLACEHOLDER_')) {
        // Extract chapter ID from the text
        const match = text.match(/IMAGE_SUGGESTION_PLACEHOLDER_([^_]+)_(\d+)_(.+)/);
        if (match) {
          const [, chapterId, imageCount, searchQuery] = match;
          const cleanSearchQuery = searchQuery.replace(/_/g, ' ').replace(/🖼️/g, '').trim();

          // Style the paragraph as an image suggestion card
          p.style.cssText = `
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px dashed #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
          `;

          // Add data attributes for event handling
          p.setAttribute('data-image-suggestion', chapterId);
          p.setAttribute('data-search-query', cleanSearchQuery);
          p.setAttribute('data-image-count', imageCount);
          p.className = 'image-suggestion-styled';

          // Replace the text content with styled content
          p.innerHTML = `
            🖼️ <strong>Image suggestions available</strong><br>
            <span style="color: #1e40af; font-size: 14px;">
              ${imageCount} images found for "${cleanSearchQuery}" • Click to view
            </span>
          `;
        }
      }
    });
  }, [editor]);

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content) {
      // CRITICAL FIX: Only update editor content for initial AI content loading
      // Skip setContent() calls when user has made edits (prevents cursor jumping)
      if (!content.editorHTML) {
        // Check if content has changed to prevent unnecessary updates
        const contentString = JSON.stringify(content);
        if (lastContentRef.current === contentString && contentSetRef.current) {
          return; // Content hasn't changed, skip update
        }

        // CRITICAL FIX: In read-only mode, strip any existing image card HTML to prevent prop corruption
        let newHTML = convertAIContentToHTML(content, imageSuggestions, isReadOnly);
        if (isReadOnly) {
          // Remove any image suggestion card HTML that might cause React prop errors
          newHTML = newHTML.replace(/<div[^>]*data-type="image-suggestion-card"[^>]*><\/div>/g, '');
        }

        // CRITICAL FIX: Prevent infinite loop by temporarily disabling onUpdate callback
        const originalOnUpdate = editor.options.onUpdate;
        editor.options.onUpdate = null;
        
        editor.commands.setContent(newHTML);
        
        // Mark content as set and store current content
        contentSetRef.current = true;
        lastContentRef.current = contentString;
        
        // Restore onUpdate callback after content is set
        editor.options.onUpdate = originalOnUpdate;

        // Process placeholders after content is set (only in edit mode)
        if (!isReadOnly) {
          setTimeout(() => {
            processImageSuggestionPlaceholders();
          }, 100);
        }
      }
    }
  }, [editor, content, imageSuggestions, isReadOnly, processImageSuggestionPlaceholders]);



  // Focus restoration effect - handles focus when menu closes
  useEffect(() => {
    // This effect runs when isMenuExpanded changes from true to false
    // We restore focus to maintain cursor position after menu interactions
    if (!isMenuExpanded && editor && !editor.isFocused) {
      try {
        editor.commands.focus();
      } catch (error) {
        console.warn('Could not restore editor focus:', error);
      }
    }
  }, [isMenuExpanded, editor]);

  // Cleanup effect for menu visibility timeout
  useEffect(() => {
    return () => {
      // Clear timeout on component unmount or when showFloatingMenu changes
      if (menuVisibilityTimeoutRef.current) {
        clearTimeout(menuVisibilityTimeoutRef.current);
        menuVisibilityTimeoutRef.current = null;
      }
    };
  }, [showFloatingMenu]);

  // Handle click outside to close URL input
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showImageUrlInput) {
        // Check if click is outside the URL input component
        const urlInputElement = event.target.closest('.fixed.z-50');
        if (!urlInputElement) {
          setShowImageUrlInput(false);
        }
      }
    };

    if (showImageUrlInput) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showImageUrlInput]);

  // Clean up interactive states when switching to read-only mode
  useEffect(() => {
    if (isReadOnly) {
      console.log('🔒 Read-only mode activated: Cleaning up interactive states');

      // Reset all floating menu states
      setShowFloatingMenu(false);
      closeExpandedMenu('read-only-mode', false); // Don't preserve focus in read-only mode

      // Reset all image-related states
      setShowImageUrlInput(false);
      setShowContextualImageModal(false);
      setContextualImageContext(null);

      console.log('🔒 Interactive states cleaned up for read-only mode');
    }
  }, [isReadOnly]);

  // Handle clicks on image suggestion placeholders
  useEffect(() => {
    const handlePlaceholderClick = (event) => {
      // Check if click is on a styled image suggestion placeholder
      const placeholder = event.target.closest('.image-suggestion-styled');

      if (placeholder) {
        event.preventDefault();
        event.stopPropagation();

        const chapterId = placeholder.dataset.imageSuggestion;
        const searchQuery = placeholder.dataset.searchQuery;
        const imageCount = parseInt(placeholder.dataset.imageCount) || 0;

        console.log('🎯 Image suggestion placeholder clicked:', {
          chapterId,
          searchQuery,
          imageCount
        });

        // Find the chapter data from imageSuggestions (simplified approach)
        const chapterData = imageSuggestions[chapterId];

        if (chapterData && chapterData.images?.length > 0) {
          // Create a simple placement object for chapter-boundary placement
          const placement = {
            id: 'chapter-boundary',
            type: 'chapter-boundary',
            position: 'before-chapter',
            description: chapterData.description || `Image suggestions for ${chapterData.chapterTitle}`,
            contextualHint: chapterData.contextualHint || `Visual content for ${chapterData.chapterTitle}`
          };

          handleContextualImageCardClick(chapterId, 'chapter-boundary', placement);
        } else {
          console.warn('Could not find chapter data or images for placeholder:', { chapterId });
        }
      }
    };

    // Add event listener to document
    document.addEventListener('click', handlePlaceholderClick);

    return () => {
      document.removeEventListener('click', handlePlaceholderClick);
    };
  }, [imageSuggestions]); // Re-run when imageSuggestions change

  // Handle menu positioning and visibility with useCallback to prevent stale closures
  const handleSelectionUpdate = useCallback(() => {
    if (!editor) return;

    const updateId = Math.random().toString(36).substr(2, 9);

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();



    // Show menu for all supported node types
    const supportedNodeTypes = ['paragraph', 'heading', 'listItem', 'blockquote', 'codeBlock', 'image'];
    if (supportedNodeTypes.includes(currentNode.type.name)) {
      // Get the DOM element for positioning - find the actual content block element
      const { view } = editor;
      const start = $from.start();
      const dom = view.domAtPos(start);

      // Find the actual content block element (paragraph, heading, etc.)
      let element = dom.node;

      // If we got a text node, find its parent element
      if (element.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      // Find the actual content block by looking for the paragraph/heading element
      while (element && !['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'LI', 'BLOCKQUOTE', 'PRE', 'DIV'].includes(element.tagName)) {
        element = element.parentElement;
      }

      if (element) {
        const rect = element.getBoundingClientRect();



        // Store element reference for scroll-aware positioning
        currentTargetElementRef.current = element;
        currentNodePositionRef.current = $from.pos;

        // Responsive positioning logic
        const viewportWidth = window.innerWidth;
        const availableLeftSpace = rect.left;
        const buttonWidth = 32; // w-8 = 32px
        const minOffset = 10; // Minimum space from content

        // Calculate positioning similar to Designrr's approach
        // Use negative left positioning relative to the content block
        let leftOffset;

        if (viewportWidth <= 768) {
          // Mobile: Smaller offset to prevent going off-screen
          leftOffset = 16; // -left-4 equivalent (16px)
        } else if (viewportWidth <= 1024) {
          // Tablet: Medium offset
          leftOffset = 32; // -left-8 equivalent (32px)
        } else {
          // Desktop: Full offset like Designrr (-50px)
          leftOffset = 50;
        }

        // Position to the left of the content block (absolute positioning relative to document)
        // For absolute positioning, we need to account for the parent container's position
        const editorContainer = document.querySelector('.tiptap-editor');
        const containerRect = editorContainer ? editorContainer.getBoundingClientRect() : { top: 0, left: 0 };

        const buttonPosition = {
          top: rect.top - containerRect.top,
          left: rect.left - containerRect.left - leftOffset
        };





        // Calculate expanded menu position using smart positioning
        const expandedPosition = calculateExpandedMenuPosition(rect, buttonPosition);

        setMenuPosition(buttonPosition);
        setExpandedMenuPosition(expandedPosition);
        setShowFloatingMenu(true);
      }
    } else {
      setShowFloatingMenu(false);
      // Clear element reference when menu is hidden
      currentTargetElementRef.current = null;
      currentNodePositionRef.current = null;
    }

    // Close expanded menu when selection changes (but not immediately after opening)

    if (isMenuExpanded && !preventAutoClose) {
      closeExpandedMenu('selection-change', false); // Focus will be handled by the focus restoration effect
    } else if (preventAutoClose) {
      setPreventAutoClose(false); // Reset the flag after one cycle
    }
  }, [editor, isMenuExpanded, preventAutoClose]); // Include all dependencies to prevent stale closures

  const handleUpdate = useCallback(() => {
    // Close expanded menu when editor content changes
    if (isMenuExpanded) {
      closeExpandedMenu('content-update', false); // Don't preserve focus since content is being updated
    }
  }, [isMenuExpanded, closeExpandedMenu]); // Include closeExpandedMenu dependency

  useEffect(() => {
    if (!editor) return;

    // Listen for editor updates and selection changes
    editor.on('update', handleUpdate);
    editor.on('selectionUpdate', handleSelectionUpdate);

    // REMOVED: Initial check that was interfering with cursor positioning
    // handleSelectionUpdate(); // This was causing cursor to jump after Enter key

    return () => {
      editor.off('update', handleUpdate);
      editor.off('selectionUpdate', handleSelectionUpdate);
    };
  }, [editor, handleUpdate, handleSelectionUpdate]); // Include callback dependencies

  return (
    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 overflow-auto">
      {/* Main Content Container */}
      <div className="min-h-full py-6 sm:py-8 px-4">
        {/* Document Canvas */}
        <div className="max-w-4xl mx-auto">
          {/* Document Paper */}
          <div className="bg-white shadow-lg hover:shadow-xl rounded-lg min-h-[800px] p-6 sm:p-8 transition-all duration-300 border border-gray-200 hover:border-blue-200 focus-within:border-blue-400 focus-within:shadow-xl focus-within:shadow-blue-100/50">
            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center min-h-[700px] animate-pulse">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-3 border-blue-600 mx-auto mb-6 shadow-lg"></div>
                  <p className="text-gray-600 text-lg font-medium">Loading content...</p>
                  <p className="text-gray-400 text-sm mt-2">Converting AI-generated content</p>
                </div>
              </div>
            )}



            {/* Editor Content with enhanced styling */}
            {!isLoading && (
              <div className="relative">
                <EditorContent
                  editor={editor}
                  className="min-h-[700px] focus:outline-none tiptap-editor transition-all duration-200"
                  onClick={(e) => {
                    console.log('🖱️ EDITOR CLICK EVENT:', {
                      target: e.target.tagName,
                      targetClass: e.target.className,
                      clientX: e.clientX,
                      clientY: e.clientY,
                      timestamp: new Date().toISOString(),
                      isReadOnly
                    });

                    // Prevent floating menu interactions in read-only mode
                    if (isReadOnly) {
                      console.log('🔒 Read-only mode: Skipping floating menu logic');
                      return;
                    }
                  }}
                />

                {/* Custom Floating Menu for Both Empty and Content Blocks */}
                {editor && showFloatingMenu && !isReadOnly && (() => {
                  const menuType = getCurrentMenuType();
                  return (
                    <>
                      {/* Always show the trigger button */}
                      <div
                        ref={triggerButtonRef}
                        className="absolute z-50 flex justify-center items-center -my-1.5"
                        style={{
                          top: `${menuPosition.top}px`,
                          left: `${menuPosition.left}px`,
                        }}
                      >
                        {menuType === 'plus' ? (
                          <button
                            onClick={(e) => {
                              // Prevent interactions in read-only mode
                              if (isReadOnly) {
                                console.log('🔒 Read-only mode: Plus button click ignored');
                                return;
                              }

                              e.preventDefault();
                              e.stopPropagation();

                              // Toggle behavior: if menu is already expanded, close it
                              if (isMenuExpanded) {
                                closeExpandedMenu('button-toggle', false);
                              } else {
                                setPreventAutoClose(true);
                                setIsMenuExpanded(true);

                                // Reset preventAutoClose after a brief delay to allow click-outside detection
                                setTimeout(() => {
                                  setPreventAutoClose(false);
                                }, 100);
                              }
                            }}
                            className="p-2 flex shrink-0 gap-2 hover:bg-gray-50 items-center justify-center rounded-lg transition-colors whitespace-nowrap border border-gray-200 shadow-lg bg-white text-blue-600 select-none"
                            title="Add content"
                          >
                            <span className="text-blue-600 text-lg font-bold leading-none">+</span>
                          </button>
                        ) : (
                          <button
                            onClick={(e) => {
                              // Prevent interactions in read-only mode
                              if (isReadOnly) {
                                console.log('🔒 Read-only mode: Ellipsis button click ignored');
                                return;
                              }

                              e.preventDefault();
                              e.stopPropagation();

                              // Toggle behavior: if menu is already expanded, close it
                              if (isMenuExpanded) {
                                closeExpandedMenu('button-toggle', false);
                              } else {
                                setPreventAutoClose(true);
                                setIsMenuExpanded(true);

                                // Reset preventAutoClose after a brief delay to allow click-outside detection
                                setTimeout(() => {
                                  setPreventAutoClose(false);
                                }, 100);
                              }
                            }}
                            className="p-2 flex shrink-0 gap-2 hover:bg-gray-50 items-center justify-center rounded-lg transition-colors whitespace-nowrap border border-gray-200 shadow-lg bg-white text-gray-600 select-none"
                            title="Edit content"
                          >
                            <span className="text-gray-600 text-sm font-bold leading-none">⋯</span>
                          </button>
                        )}
                      </div>

                      {/* Show expanded menu when isMenuExpanded is true */}
                      {isMenuExpanded && (
                        <div
                          ref={expandedMenuRef}
                          className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-2 min-w-[200px]"
                          style={{
                            top: `${expandedMenuPosition.top}px`,
                            left: `${expandedMenuPosition.left}px`,
                          }}
                        >
                          {/* Header with Plus or Ellipsis Icon */}
                          <div className="flex items-center mb-2 pb-2 border-b border-gray-100">
                            {getCurrentMenuType() === 'plus' ? (
                              <>
                                <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2">
                                  <span className="text-white text-sm font-bold">+</span>
                                </div>
                                <span className="text-sm text-gray-600 font-medium">Add content</span>
                              </>
                            ) : (
                              <>
                                <div className="w-6 h-6 bg-gray-600 rounded flex items-center justify-center mr-2">
                                  <span className="text-white text-xs font-bold">⋯</span>
                                </div>
                                <span className="text-sm text-gray-600 font-medium">Edit content</span>
                              </>
                            )}
                          </div>

                          {/* Menu Options */}
                          <div className="space-y-1">
                            {renderNodeSpecificMenuOptions(getCurrentNodeType(), getCurrentMenuType() === 'plus')}
                          </div>
                        </div>
                      )}
                    </>
                  );
                })()}

                {/* Subtle visual indicator for editor focus */}
                <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-400 to-blue-600 rounded-full opacity-0 transition-opacity duration-300 focus-within:opacity-100 -ml-4"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Image URL Input Component - Only show in edit mode */}
      {!isReadOnly && showImageUrlInput && (
        <div
          className="fixed z-50"
          style={{
            top: `${imageUrlInputPosition.top}px`,
            left: `${imageUrlInputPosition.left}px`,
          }}
        >
          <ImageUrlInput
            isVisible={showImageUrlInput}
            onImageInsert={handleImageInsert}
            onCancel={handleImageUrlCancel}
          />
        </div>
      )}

      {/* Contextual Image Selection Modal - Only show in edit mode */}
      {!isReadOnly && (
        <ContextualImageSelectionModal
          isOpen={showContextualImageModal}
          onClose={handleContextualImageModalClose}
          onImageSelect={handleContextualImageSelect}
          chapterId={contextualImageContext?.chapterId}
          placementId={contextualImageContext?.placementId}
          imageSuggestions={imageSuggestions}
          placement={contextualImageContext?.placement}
        />
      )}

      {/* Enhanced CSS for placeholder styling and editor improvements */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Placeholder styling */
          .tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
            content: attr(data-placeholder);
            float: left;
            color: #9ca3af;
            pointer-events: none;
            height: 0;
            font-style: italic;
            opacity: 0.8;
            transition: opacity 0.2s ease;
          }

          .tiptap-editor .ProseMirror p.is-empty::before {
            content: attr(data-placeholder);
            float: left;
            color: #9ca3af;
            pointer-events: none;
            height: 0;
            font-style: italic;
            opacity: 0.8;
            transition: opacity 0.2s ease;
          }

          /* Enhanced editor styling */
          .tiptap-editor .ProseMirror {
            outline: none;
            line-height: 1.75;
            letter-spacing: 0.025em;
          }

          /* Improved focus states for headings */
          .tiptap-editor .ProseMirror h1:hover,
          .tiptap-editor .ProseMirror h2:hover,
          .tiptap-editor .ProseMirror h3:hover {
            color: #2563eb;
            transition: color 0.2s ease;
          }

          /* Better list styling */
          .tiptap-editor .ProseMirror ul,
          .tiptap-editor .ProseMirror ol {
            padding-left: 1.5rem;
          }

          /* Responsive text sizing */
          @media (max-width: 640px) {
            .tiptap-editor .ProseMirror {
              font-size: 16px;
              line-height: 1.6;
            }
          }

          /* Selection styling */
          .tiptap-editor .ProseMirror ::selection {
            background-color: #dbeafe;
            color: #1e40af;
          }

          /* Floating Menu Styling */
          .floating-menu-container {
            z-index: 50;
          }

          /* Responsive floating menu adjustments */
          @media (max-width: 768px) {
            .floating-menu-container {
              /* Mobile: Smaller buttons and closer positioning */
            }
          }

          @media (min-width: 769px) and (max-width: 1024px) {
            .floating-menu-container {
              /* Tablet/Laptop: Adjusted positioning to prevent overlap */
            }
          }

          @media (min-width: 1025px) {
            .floating-menu-container {
              /* Desktop: Full positioning freedom */
            }
          }

          .floating-menu-container .tippy-box {
            background: transparent;
            border: none;
            box-shadow: none;
          }

          .floating-menu-container .tippy-content {
            padding: 0;
          }

          /* Plus button styling */
          .floating-menu-container button:first-child {
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
          }

          .floating-menu-container button:first-child:hover {
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
          }

          /* Hover effects for menu items */
          .floating-menu-container button:hover {
            background-color: #f9fafb;
            transform: translateX(2px);
            transition: all 0.15s ease;
          }

          /* Active states for menu items */
          .floating-menu-container button:active {
            background-color: #f3f4f6;
            transform: translateX(1px);
          }

          /* Expanded menu animation */
          .floating-menu-container > div {
            animation: menuSlideIn 0.2s ease-out;
          }

          @keyframes menuSlideIn {
            from {
              opacity: 0;
              transform: translateX(-10px) scale(0.95);
            }
            to {
              opacity: 1;
              transform: translateX(0) scale(1);
            }
          }
        `
      }} />
    </div>
  );
};

export default DocumentCanvasMinimal;
