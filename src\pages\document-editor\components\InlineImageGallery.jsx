import React, { useState } from 'react';

/**
 * InlineImageGallery - Designrr-inspired inline image selection component
 * 
 * This component replicates Designrr's approach of showing images directly
 * within the suggestion card with hover effects and direct selection.
 * 
 * Features:
 * - Overlapping image layout with hover effects
 * - Scale and lift animations on hover
 * - Direct image selection without modal
 * - Close button functionality
 * - Gradient background matching Designrr's design
 */
const InlineImageGallery = (props) => {
  // Debug: Check for any malformed props
  if (process.env.NODE_ENV === 'development') {
    const expectedProps = [
      'images', 'chapterId', 'placementId', 'searchQuery', 'chapterTitle',
      'onImageSelect', 'onClose', 'maxImages'
    ];
    const unexpectedProps = Object.keys(props).filter(key => !expectedProps.includes(key));
    if (unexpectedProps.length > 0) {
      console.warn('🚨 InlineImageGallery received unexpected props:', unexpectedProps, props);
    }
  }

  // Safely destructure props
  const {
    images = [],
    chapterId,
    placementId,
    searchQuery = '',
    chapterTitle = '',
    onImageSelect,
    onClose,
    maxImages = 3
  } = props;
  const [selectedImageId, setSelectedImageId] = useState(null);

  // Limit images to display (Designrr shows 3)
  const displayImages = images.slice(0, maxImages);

  const handleImageClick = (image, index) => {
    console.log('🖼️ Image selected from inline gallery:', {
      image,
      index,
      chapterId,
      placementId
    });

    setSelectedImageId(image.id);
    
    // Call the selection handler after a brief visual feedback
    setTimeout(() => {
      if (onImageSelect) {
        onImageSelect(image, chapterId, placementId);
      }
    }, 150);
  };

  const handleCloseClick = (e) => {
    e.stopPropagation();
    console.log('❌ Closing inline image gallery');
    if (onClose) {
      onClose();
    }
  };

  if (!displayImages.length) {
    return (
      <div className="ai-images-wrapper mt-6 pt-20 relative">
        <div
          className="pt-28 pb-6 border border-gray-200 rounded-lg shadow-lg relative"
          style={{
            background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)'
          }}
        >
          <div className="text-center text-gray-500">
            No images available for "{searchQuery}"
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="ai-images-wrapper mt-6 pt-20 relative">
      {/* Floating Images Gallery - Designrr Style */}
      <div className="absolute top-8 left-1/2 -translate-x-1/2 z-10">
        <div className="flex justify-center">
          {displayImages.map((image, index) => (
            <img
              key={image.id}
              className={`
                h-32 min-w-52 max-w-52 -mx-2 object-cover rounded-lg
                outline outline-1 outline-gray-200
                transition-all duration-300 ease-in-out cursor-pointer
                hover:z-10 hover:-translate-y-5 hover:scale-115
                ${selectedImageId === image.id ? 'ring-4 ring-blue-500 ring-opacity-75' : ''}
              `.trim()}
              data-image-index={index + 1}
              draggable={false}
              src={image.thumbnailUrl || image.url}
              alt={image.description || `Image ${index + 1} for ${searchQuery}`}
              onClick={() => handleImageClick(image, index)}
              onError={(e) => {
                console.warn('Image failed to load:', image.url);
                e.target.style.display = 'none';
              }}
            />
          ))}
        </div>
      </div>

      {/* Card Background with Gradient - Designrr Style */}
      <div
        className="select-none pt-28 pb-6 border border-gray-200 rounded-lg shadow-lg relative z-0"
        style={{
          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)'
        }}
      >
        {/* Close Button */}
        <div 
          className="absolute top-5 right-5 cursor-pointer w-5 h-5 text-xl text-gray-400 rounded hover:bg-gray-100 leading-none flex items-center justify-center"
          onClick={handleCloseClick}
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M6 18L18 6M6 6l12 12" 
            />
          </svg>
        </div>

        {/* Card Content */}
        <div className="text-center font-semibold text-gray-900">
          Add to this chapter an image from the suggested or{' '}
          <button className="inline-flex items-center gap-1.5 font-medium whitespace-nowrap leading-4 select-none cursor-pointer text-blue-600 hover:underline">
            upload your own
          </button>
        </div>
        <div className="text-center font-light italic text-sm text-gray-600 mt-1">
          All AI suggestions are saved to your media ({displayImages.length} available)
        </div>
      </div>


    </div>
  );
};

export default InlineImageGallery;
