/**
 * Test utility for validating chapter-based image card placement
 * Tests the new placement logic that targets chapter headings
 */

import { injectChapterImageCards } from './contentConverter.js';

/**
 * Mock HTML content with chapter structure for testing
 */
const mockHTMLContent = `
<h1>Document Title</h1>
<p>This is the introduction paragraph.</p>

<h2>Chapter 1: Introduction to the Topic</h2>
<p>This is the first chapter content. It should NOT have an image card before it.</p>
<p>More content for chapter 1.</p>

<h2>Chapter 2: Understanding the Basics</h2>
<p>This is the second chapter content. It SHOULD have an image card before it.</p>
<p>More content for chapter 2.</p>

<h2>Chapter 3: Advanced Concepts</h2>
<p>This is the third chapter content. It SHOULD have an image card before it.</p>
<p>More content for chapter 3.</p>

<h2>Chapter 4: Practical Applications</h2>
<p>This is the fourth chapter content. It SHOULD have an image card before it.</p>
<p>More content for chapter 4.</p>

<h2>Conclusion</h2>
<p>This is the conclusion content.</p>
`;

/**
 * Mock image suggestions data
 */
const mockImageSuggestions = {
  'chapter-1': {
    chapterTitle: 'Introduction to the Topic',
    chapterNumber: 1,
    searchQuery: 'introduction topic basics',
    images: [
      { id: 'img1', url: 'https://example.com/img1.jpg', description: 'Introduction image' },
      { id: 'img2', url: 'https://example.com/img2.jpg', description: 'Topic overview' },
      { id: 'img3', url: 'https://example.com/img3.jpg', description: 'Getting started' }
    ],
    description: 'Image suggestions for Introduction',
    contextualHint: 'Visual content for Chapter 1'
  },
  'chapter-2': {
    chapterTitle: 'Understanding the Basics',
    chapterNumber: 2,
    searchQuery: 'understanding basics fundamentals',
    images: [
      { id: 'img4', url: 'https://example.com/img4.jpg', description: 'Basic concepts' },
      { id: 'img5', url: 'https://example.com/img5.jpg', description: 'Fundamentals' },
      { id: 'img6', url: 'https://example.com/img6.jpg', description: 'Core principles' }
    ],
    description: 'Image suggestions for Understanding the Basics',
    contextualHint: 'Visual content for Chapter 2'
  },
  'chapter-3': {
    chapterTitle: 'Advanced Concepts',
    chapterNumber: 3,
    searchQuery: 'advanced concepts techniques',
    images: [
      { id: 'img7', url: 'https://example.com/img7.jpg', description: 'Advanced techniques' },
      { id: 'img8', url: 'https://example.com/img8.jpg', description: 'Complex concepts' },
      { id: 'img9', url: 'https://example.com/img9.jpg', description: 'Expert level' }
    ],
    description: 'Image suggestions for Advanced Concepts',
    contextualHint: 'Visual content for Chapter 3'
  },
  'chapter-4': {
    chapterTitle: 'Practical Applications',
    chapterNumber: 4,
    searchQuery: 'practical applications real world',
    images: [
      { id: 'img10', url: 'https://example.com/img10.jpg', description: 'Real world examples' },
      { id: 'img11', url: 'https://example.com/img11.jpg', description: 'Practical use cases' },
      { id: 'img12', url: 'https://example.com/img12.jpg', description: 'Implementation' }
    ],
    description: 'Image suggestions for Practical Applications',
    contextualHint: 'Visual content for Chapter 4'
  }
};

/**
 * Test the chapter-based placement logic
 */
export const testChapterBasedPlacement = () => {
  console.log('🧪 Testing Chapter-Based Image Card Placement...\n');
  
  // Test the injection function
  const result = injectChapterImageCards(mockHTMLContent, mockImageSuggestions);
  
  // Analyze the results
  console.log('📊 Placement Analysis:');
  
  // Check if Chapter 1 has NO image card before it
  const chapter1Match = result.match(/(<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>)[\s\S]*?<h2>Chapter 1:/);
  const hasCardBeforeChapter1 = !!chapter1Match;
  console.log(`   Chapter 1: ${hasCardBeforeChapter1 ? '❌ HAS card (should NOT)' : '✅ NO card (correct)'}`);
  
  // Check if Chapter 2 has an image card before it
  const chapter2Match = result.match(/(<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>)[\s\S]*?<h2>Chapter 2:/);
  const hasCardBeforeChapter2 = !!chapter2Match;
  console.log(`   Chapter 2: ${hasCardBeforeChapter2 ? '✅ HAS card (correct)' : '❌ NO card (should have)'}`);
  
  // Check if Chapter 3 has an image card before it
  const chapter3Match = result.match(/(<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>)[\s\S]*?<h2>Chapter 3:/);
  const hasCardBeforeChapter3 = !!chapter3Match;
  console.log(`   Chapter 3: ${hasCardBeforeChapter3 ? '✅ HAS card (correct)' : '❌ NO card (should have)'}`);
  
  // Check if Chapter 4 has an image card before it
  const chapter4Match = result.match(/(<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>)[\s\S]*?<h2>Chapter 4:/);
  const hasCardBeforeChapter4 = !!chapter4Match;
  console.log(`   Chapter 4: ${hasCardBeforeChapter4 ? '✅ HAS card (correct)' : '❌ NO card (should have)'}`);
  
  // Count total image cards
  const cardMatches = result.match(/data-type="image-suggestion-card"/g) || [];
  const totalCards = cardMatches.length;
  const expectedCards = 3; // Should be 3 cards (chapters 2, 3, 4)
  console.log(`\n📈 Total Cards: ${totalCards} (expected: ${expectedCards}) ${totalCards === expectedCards ? '✅' : '❌'}`);
  
  // Test edge cases
  console.log('\n🔍 Edge Case Tests:');
  
  // Test with empty image suggestions
  const emptyResult = injectChapterImageCards(mockHTMLContent, {});
  const emptyCards = (emptyResult.match(/data-type="image-suggestion-card"/g) || []).length;
  console.log(`   Empty suggestions: ${emptyCards === 0 ? '✅' : '❌'} (${emptyCards} cards)`);
  
  // Test with no HTML
  const noHtmlResult = injectChapterImageCards('', mockImageSuggestions);
  console.log(`   No HTML: ${noHtmlResult === '' ? '✅' : '❌'} (returned empty string)`);
  
  // Test with malformed HTML
  const malformedHTML = '<p>No chapters here</p>';
  const malformedResult = injectChapterImageCards(malformedHTML, mockImageSuggestions);
  const malformedCards = (malformedResult.match(/data-type="image-suggestion-card"/g) || []).length;
  console.log(`   No chapters: ${malformedCards === 0 ? '✅' : '❌'} (${malformedCards} cards, should fallback)`);
  
  return {
    success: !hasCardBeforeChapter1 && hasCardBeforeChapter2 && hasCardBeforeChapter3 && hasCardBeforeChapter4,
    totalCards,
    expectedCards,
    result
  };
};

/**
 * Test different chapter heading formats
 */
export const testChapterHeadingFormats = () => {
  console.log('\n🎯 Testing Different Chapter Heading Formats...\n');
  
  const testCases = [
    {
      name: 'Standard Format',
      html: '<h2>Chapter 2: Understanding Basics</h2><p>Content</p>',
      shouldMatch: true
    },
    {
      name: 'No Colon',
      html: '<h2>Chapter 2 Understanding Basics</h2><p>Content</p>',
      shouldMatch: true
    },
    {
      name: 'H1 Tag',
      html: '<h1>Chapter 3: Advanced Topics</h1><p>Content</p>',
      shouldMatch: true
    },
    {
      name: 'Extra Spaces',
      html: '<h2>  Chapter   4  :   Final Chapter  </h2><p>Content</p>',
      shouldMatch: true
    },
    {
      name: 'Not a Chapter',
      html: '<h2>Introduction</h2><p>Content</p>',
      shouldMatch: false
    }
  ];
  
  testCases.forEach(testCase => {
    const suggestions = {
      'chapter-2': mockImageSuggestions['chapter-2'],
      'chapter-3': mockImageSuggestions['chapter-3'],
      'chapter-4': mockImageSuggestions['chapter-4']
    };
    
    const result = injectChapterImageCards(testCase.html, suggestions);
    const hasCard = result.includes('data-type="image-suggestion-card"');
    
    const status = hasCard === testCase.shouldMatch ? '✅' : '❌';
    console.log(`   ${testCase.name}: ${status} (${hasCard ? 'has' : 'no'} card, expected: ${testCase.shouldMatch ? 'has' : 'no'} card)`);
  });
};

/**
 * Run all tests
 */
export const runChapterPlacementTests = () => {
  console.log('🚀 Starting Chapter-Based Image Card Placement Tests...\n');
  
  const mainTest = testChapterBasedPlacement();
  testChapterHeadingFormats();
  
  console.log('\n📋 Test Summary:');
  console.log(`   Main Test: ${mainTest.success ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   Cards Placed: ${mainTest.totalCards}/${mainTest.expectedCards}`);
  
  if (mainTest.success) {
    console.log('\n🎉 All tests passed! Chapter-based placement is working correctly.');
    console.log('\n📝 Next Steps:');
    console.log('1. Test in the browser with a real document');
    console.log('2. Create a document with multiple chapters');
    console.log('3. Enable image suggestions and verify placement');
    console.log('4. Confirm image cards appear before Chapter 2, 3, etc. but not Chapter 1');
  } else {
    console.log('\n💥 Some tests failed. Check the implementation.');
  }
  
  return mainTest;
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testChapterPlacement = {
    testChapterBasedPlacement,
    testChapterHeadingFormats,
    runChapterPlacementTests
  };
}
