/**
 * Test utility for validating enhanced image suggestion functionality
 * Use this to test the mock AI system and workflow integration
 */

import { generateImageSuggestions } from '../services/unsplashService.js';

/**
 * Test data for validating image suggestions
 */
const mockDocumentData = {
  topicAndNiche: {
    mainTopic: 'Digital Marketing Strategy',
    subNiches: [
      { name: 'Social Media Marketing', description: 'Social platforms and engagement' },
      { name: 'Content Marketing', description: 'Content creation and distribution' }
    ]
  },
  audienceAnalysis: {
    primaryAudience: 'Business Professionals'
  },
  documentPurpose: {
    primaryType: 'business'
  }
};

const mockOutline = {
  chapters: [
    {
      number: 1,
      title: 'Introduction to Digital Marketing',
      sections: ['Overview', 'Key Concepts', 'Market Trends']
    },
    {
      number: 2,
      title: 'Social Media Strategy Development',
      sections: ['Platform Selection', 'Content Planning', 'Engagement Tactics']
    },
    {
      number: 3,
      title: 'Content Creation and Management',
      sections: ['Content Types', 'Creation Workflow', 'Quality Control']
    },
    {
      number: 4,
      title: 'Analytics and Performance Measurement',
      sections: ['Key Metrics', 'Tracking Tools', 'ROI Analysis']
    }
  ]
};

/**
 * Test the enhanced image suggestion system
 */
export const testImageSuggestions = async () => {
  console.log('🧪 Testing Enhanced Image Suggestion System...');
  
  try {
    const suggestions = await generateImageSuggestions(mockDocumentData, mockOutline);
    
    console.log('✅ Image suggestions generated successfully!');
    console.log(`📊 Generated suggestions for ${Object.keys(suggestions).length} chapters`);
    
    // Validate each chapter's suggestions
    Object.entries(suggestions).forEach(([chapterId, chapterData]) => {
      console.log(`\n📖 Chapter ${chapterData.chapterNumber}: ${chapterData.chapterTitle}`);
      console.log(`🔍 Search Query: "${chapterData.searchQuery}"`);
      console.log(`🖼️ Images: ${chapterData.images.length}`);
      
      // Validate image data structure
      chapterData.images.forEach((image, index) => {
        console.log(`  Image ${index + 1}:`);
        console.log(`    - ID: ${image.id}`);
        console.log(`    - Description: ${image.description}`);
        console.log(`    - Photographer: ${image.photographer}`);
        console.log(`    - URL: ${image.url.substring(0, 50)}...`);
        
        // Validate Unsplash structure
        const hasRequiredFields = image.id && image.url && image.thumbnailUrl && 
                                 image.description && image.photographer;
        console.log(`    - Valid Structure: ${hasRequiredFields ? '✅' : '❌'}`);
      });
    });
    
    return suggestions;
    
  } catch (error) {
    console.error('❌ Error testing image suggestions:', error);
    throw error;
  }
};

/**
 * Test contextual relevance of image suggestions
 */
export const testContextualRelevance = async () => {
  console.log('\n🎯 Testing Contextual Relevance...');
  
  const testCases = [
    {
      topic: 'Business Strategy',
      chapterTitle: 'Strategic Planning and Analysis',
      expectedCategory: 'business'
    },
    {
      topic: 'Web Development',
      chapterTitle: 'JavaScript Programming Fundamentals',
      expectedCategory: 'technology'
    },
    {
      topic: 'Online Learning',
      chapterTitle: 'Educational Content Creation',
      expectedCategory: 'education'
    },
    {
      topic: 'Graphic Design',
      chapterTitle: 'Creative Workflow and Inspiration',
      expectedCategory: 'creative'
    }
  ];
  
  for (const testCase of testCases) {
    const mockData = {
      topicAndNiche: { mainTopic: testCase.topic, subNiches: [] }
    };
    
    const mockChapterOutline = {
      chapters: [{
        number: 1,
        title: testCase.chapterTitle,
        sections: []
      }]
    };
    
    try {
      const suggestions = await generateImageSuggestions(mockData, mockChapterOutline);
      const chapterSuggestion = suggestions['chapter-1'];
      
      console.log(`\n📝 Test Case: ${testCase.topic} - ${testCase.chapterTitle}`);
      console.log(`🔍 Generated Query: "${chapterSuggestion.searchQuery}"`);
      console.log(`🎨 Expected Category: ${testCase.expectedCategory}`);
      
      // Check if images seem contextually appropriate
      const firstImage = chapterSuggestion.images[0];
      const isContextual = firstImage.id.includes(testCase.expectedCategory) ||
                          firstImage.description.toLowerCase().includes(testCase.expectedCategory);
      
      console.log(`✅ Contextual Match: ${isContextual ? 'YES' : 'PARTIAL'}`);
      
    } catch (error) {
      console.error(`❌ Error in test case ${testCase.topic}:`, error);
    }
  }
};

/**
 * Run all tests
 */
export const runAllTests = async () => {
  console.log('🚀 Starting Image Suggestion System Tests...\n');
  
  try {
    await testImageSuggestions();
    await testContextualRelevance();
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Test the workflow in the browser');
    console.log('2. Create a new document and enable image suggestions');
    console.log('3. Verify images appear correctly in the document editor');
    console.log('4. Test image selection and insertion functionality');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error);
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testImageSuggestions = {
    testImageSuggestions,
    testContextualRelevance,
    runAllTests
  };
}
